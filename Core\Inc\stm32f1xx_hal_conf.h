/**
  ******************************************************************************
  * @file    stm32f1xx_hal_conf.h
  * @brief   STM32F1xx HAL库配置文件
  * @description 配置HAL库的模块选择、时钟参数和系统设置
  *              针对智能垃圾桶项目进行了优化配置
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __STM32F1xx_HAL_CONF_H      // 防止头文件重复包含
#define __STM32F1xx_HAL_CONF_H

#ifdef __cplusplus
 extern "C" {
#endif

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/

/* ########################## Module Selection ############################## */
/**
  * @brief HAL驱动模块选择配置
  * @note  只启用智能垃圾桶项目需要的HAL模块，减少代码体积和编译时间
  */
#define HAL_MODULE_ENABLED           // 启用HAL核心模块（必需）
#define HAL_CORTEX_MODULE_ENABLED    // 启用Cortex-M3内核相关模块（必需）
#define HAL_DMA_MODULE_ENABLED       // 启用DMA模块（可选，用于高效数据传输）
#define HAL_FLASH_MODULE_ENABLED     // 启用Flash存储器模块（必需）
#define HAL_GPIO_MODULE_ENABLED      // 启用GPIO模块（必需，控制所有引脚）
#define HAL_PWR_MODULE_ENABLED       // 启用电源管理模块（必需）
#define HAL_RCC_MODULE_ENABLED       // 启用复位和时钟控制模块（必需）
#define HAL_TIM_MODULE_ENABLED       // 启用定时器模块（必需，用于PWM和计时）
#define HAL_UART_MODULE_ENABLED      // 启用UART模块（可选，用于调试输出）

/* ########################## HSE/HSI Values adaptation ##################### */
/**
  * @brief 外部高速振荡器(HSE)频率配置
  * @note  STM32F103C8T6开发板通常使用8MHz外部晶振
  *        此值用于RCC HAL模块计算系统频率（当HSE作为系统时钟源时）
  */
#if !defined  (HSE_VALUE)
  #define HSE_VALUE    8000000U    /*!< 外部晶振频率：8MHz */
#endif /* HSE_VALUE */

#if !defined  (HSE_STARTUP_TIMEOUT)
  #define HSE_STARTUP_TIMEOUT    100U   /*!< HSE启动超时时间：100ms */
#endif /* HSE_STARTUP_TIMEOUT */

/**
  * @brief 内部高速振荡器(HSI)频率配置
  * @note  STM32F103的HSI频率为8MHz（出厂校准值）
  *        此值用于RCC HAL模块计算系统频率（当HSI作为时钟源时）
  */
#if !defined  (HSI_VALUE)
  #define HSI_VALUE    8000000U    /*!< 内部高速振荡器频率：8MHz */
#endif /* HSI_VALUE */

/**
  * @brief 内部低速振荡器(LSI)频率配置
  * @note  LSI主要用于独立看门狗(IWDG)和RTC（当LSE不可用时）
  *        频率会因电压和温度变化而有所差异
  */
#if !defined  (LSI_VALUE)
 #define LSI_VALUE  40000U       /*!< LSI典型频率：40kHz */
#endif /* LSI_VALUE */

/**
  * @brief 外部低速振荡器(LSE)频率配置
  * @note  LSE主要用于RTC时钟，通常使用32.768kHz晶振
  *        智能垃圾桶项目暂未使用RTC功能
  */
#if !defined  (LSE_VALUE)
 #define LSE_VALUE  32768U       /*!< 外部低速振荡器频率：32.768kHz */
#endif /* LSE_VALUE */

#if !defined  (LSE_STARTUP_TIMEOUT)
  #define LSE_STARTUP_TIMEOUT    5000U   /*!< LSE启动超时时间：5000ms */
#endif /* LSE_STARTUP_TIMEOUT */

/* Tip: To avoid modifying this file each time you need to use different HSE,
   ===  you can define the HSE value in your toolchain compiler preprocessor. */

/* ########################### System Configuration ######################### */
/**
  * @brief This is the HAL system configuration section
  */     
#define  VDD_VALUE                    3300U /*!< Value of VDD in mv */           
#define  TICK_INT_PRIORITY            0x0FU /*!< tick interrupt priority */
#define  USE_RTOS                     0U
#define  PREFETCH_ENABLE              1U

/* ########################## Assert Selection ############################## */
/**
  * @brief Uncomment the line below to expanse the "assert_param" macro in the 
  *        HAL drivers code
  */
/* #define USE_FULL_ASSERT    1U */

/* Includes ------------------------------------------------------------------*/
/**
  * @brief Include module's header file 
  */

#ifdef HAL_RCC_MODULE_ENABLED
 #include "stm32f1xx_hal_rcc.h"
#endif /* HAL_RCC_MODULE_ENABLED */

#ifdef HAL_GPIO_MODULE_ENABLED
 #include "stm32f1xx_hal_gpio.h"
#endif /* HAL_GPIO_MODULE_ENABLED */

#ifdef HAL_DMA_MODULE_ENABLED
  #include "stm32f1xx_hal_dma.h"
#endif /* HAL_DMA_MODULE_ENABLED */

#ifdef HAL_CORTEX_MODULE_ENABLED
 #include "stm32f1xx_hal_cortex.h"
#endif /* HAL_CORTEX_MODULE_ENABLED */

#ifdef HAL_FLASH_MODULE_ENABLED
 #include "stm32f1xx_hal_flash.h"
#endif /* HAL_FLASH_MODULE_ENABLED */

#ifdef HAL_PWR_MODULE_ENABLED
 #include "stm32f1xx_hal_pwr.h"
#endif /* HAL_PWR_MODULE_ENABLED */

#ifdef HAL_TIM_MODULE_ENABLED
 #include "stm32f1xx_hal_tim.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_UART_MODULE_ENABLED
 #include "stm32f1xx_hal_uart.h"
#endif /* HAL_UART_MODULE_ENABLED */

/* Exported macro ------------------------------------------------------------*/
#ifdef  USE_FULL_ASSERT
/**
  * @brief  The assert_param macro is used for function's parameters check.
  * @param  expr: If expr is false, it calls assert_failed function
  *         which reports the name of the source file and the source
  *         line number of the call that failed. 
  *         If expr is true, it returns no value.
  * @retval None
  */
  #define assert_param(expr) ((expr) ? (void)0U : assert_failed((uint8_t *)__FILE__, __LINE__))
/* Exported functions ------------------------------------------------------- */
  void assert_failed(uint8_t* file, uint32_t line);
#else
  #define assert_param(expr) ((void)0U)
#endif /* USE_FULL_ASSERT */

#ifdef __cplusplus
}
#endif

#endif /* __STM32F1xx_HAL_CONF_H */

/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32f1xx_it.c
  * @brief   智能垃圾桶中断服务程序实现文件
  * @description 实现系统异常和外设中断的处理函数
  *              包含Cortex-M3内核异常处理和定时器中断处理
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"                    // 包含主头文件
#include "stm32f1xx_it.h"           // 包含中断处理头文件

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
// 用户自定义头文件包含区域（当前为空）
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */
// 用户自定义类型定义区域（当前为空）
/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
// 用户自定义宏定义区域（当前为空）
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
// 用户自定义宏函数区域（当前为空）
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */
// 用户自定义私有变量区域（当前为空）
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */
// 用户自定义函数声明区域（当前为空）
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// 用户自定义代码区域（当前为空）
/* USER CODE END 0 */

/* External variables --------------------------------------------------------*/
extern TIM_HandleTypeDef htim1;     // 外部定时器1句柄（用于舵机PWM控制）
/* USER CODE BEGIN EV */
// 用户自定义外部变量区域（当前为空）
/* USER CODE END EV */

/******************************************************************************/
/*           Cortex-M3处理器中断和异常处理函数                                    */
/******************************************************************************/

/**
  * @brief 不可屏蔽中断(NMI)处理函数
  * @note  NMI是最高优先级的中断，通常用于处理严重的系统错误
  *        在智能垃圾桶系统中，此中断应该很少被触发
  */
void NMI_Handler(void)
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */
  // 用户代码：NMI中断处理开始部分（当前为空）
  /* USER CODE END NonMaskableInt_IRQn 0 */

  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  // 进入无限循环，等待系统复位或调试器介入
  while (1)
  {
    // 在此处可以添加LED闪烁等错误指示代码
  }
  /* USER CODE END NonMaskableInt_IRQn 1 */
}

/**
  * @brief 硬件错误中断处理函数
  * @note  当发生严重的硬件错误时触发，如：
  *        - 访问无效内存地址
  *        - 执行无效指令
  *        - 栈溢出等
  */
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */
  // 用户代码：硬件错误处理开始部分（当前为空）
  /* USER CODE END HardFault_IRQn 0 */

  // 进入无限循环，保持系统状态用于调试
  while (1)
  {
    /* USER CODE BEGIN W1_HardFault_IRQn 0 */
    // 在此处可以添加错误日志记录或LED指示代码
    /* USER CODE END W1_HardFault_IRQn 0 */
  }
}

/**
  * @brief 内存管理错误中断处理函数
  * @note  当MPU(内存保护单元)检测到内存访问违规时触发
  *        STM32F103C8T6没有MPU，此中断通常不会被触发
  */
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */
  // 用户代码：内存管理错误处理开始部分（当前为空）
  /* USER CODE END MemoryManagement_IRQn 0 */

  // 进入无限循环，保持系统状态用于调试
  while (1)
  {
    /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
    // 在此处可以添加内存错误处理代码
    /* USER CODE END W1_MemoryManagement_IRQn 0 */
  }
}

/**
  * @brief 总线错误中断处理函数
  * @note  当发生总线访问错误时触发，如：
  *        - 预取指令错误
  *        - 数据访问错误
  *        - 精确的数据总线错误
  */
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */
  // 用户代码：总线错误处理开始部分（当前为空）
  /* USER CODE END BusFault_IRQn 0 */

  // 进入无限循环，保持系统状态用于调试
  while (1)
  {
    /* USER CODE BEGIN W1_BusFault_IRQn 0 */
    // 在此处可以添加总线错误处理代码
    /* USER CODE END W1_BusFault_IRQn 0 */
  }
}

/**
  * @brief 用法错误中断处理函数
  * @note  当发生指令执行错误时触发，如：
  *        - 未定义指令
  *        - 非法状态
  *        - 除零错误（如果启用）
  */
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */
  // 用户代码：用法错误处理开始部分（当前为空）
  /* USER CODE END UsageFault_IRQn 0 */

  // 进入无限循环，保持系统状态用于调试
  while (1)
  {
    /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
    // 在此处可以添加用法错误处理代码
    /* USER CODE END W1_UsageFault_IRQn 0 */
  }
}

/**
  * @brief 系统服务调用中断处理函数
  * @note  通过SWI指令触发的系统服务调用
  *        在智能垃圾桶系统中通常不使用此功能
  */
void SVC_Handler(void)
{
  /* USER CODE BEGIN SVCall_IRQn 0 */
  // 用户代码：系统服务调用处理开始部分（当前为空）
  /* USER CODE END SVCall_IRQn 0 */

  /* USER CODE BEGIN SVCall_IRQn 1 */
  // 用户代码：系统服务调用处理结束部分（当前为空）
  /* USER CODE END SVCall_IRQn 1 */
}

/**
  * @brief 调试监视器中断处理函数
  * @note  用于调试器的断点和观察点功能
  *        在正常运行时此中断不会被触发
  */
void DebugMon_Handler(void)
{
  /* USER CODE BEGIN DebugMonitor_IRQn 0 */
  // 用户代码：调试监视器处理开始部分（当前为空）
  /* USER CODE END DebugMonitor_IRQn 0 */

  /* USER CODE BEGIN DebugMonitor_IRQn 1 */
  // 用户代码：调试监视器处理结束部分（当前为空）
  /* USER CODE END DebugMonitor_IRQn 1 */
}

/**
  * @brief 可挂起系统调用中断处理函数
  * @note  用于操作系统的任务切换功能
  *        在智能垃圾桶的裸机系统中不使用此功能
  */
void PendSV_Handler(void)
{
  /* USER CODE BEGIN PendSV_IRQn 0 */
  // 用户代码：可挂起系统调用处理开始部分（当前为空）
  /* USER CODE END PendSV_IRQn 0 */

  /* USER CODE BEGIN PendSV_IRQn 1 */
  // 用户代码：可挂起系统调用处理结束部分（当前为空）
  /* USER CODE END PendSV_IRQn 1 */
}

/**
  * @brief 系统滴答定时器中断处理函数
  * @note  每1ms触发一次，用于HAL库的时间基准
  *        为HAL_GetTick()函数提供时间戳，用于延时和超时检测
  */
void SysTick_Handler(void)
{
  /* USER CODE BEGIN SysTick_IRQn 0 */
  // 用户代码：系统滴答中断处理开始部分（当前为空）
  /* USER CODE END SysTick_IRQn 0 */

  HAL_IncTick();                     // 增加HAL库的时间计数器（必需）

  /* USER CODE BEGIN SysTick_IRQn 1 */
  // 用户代码：系统滴答中断处理结束部分（当前为空）
  // 可在此处添加1ms周期的任务处理代码
  /* USER CODE END SysTick_IRQn 1 */
}

/******************************************************************************/
/* STM32F1xx外设中断处理函数                                                    */
/* 在此处添加项目中使用的外设中断处理函数                                          */
/* 可用的外设中断处理函数名称请参考启动文件(startup_stm32f1xx.s)                   */
/******************************************************************************/

/**
  * @brief 定时器1更新中断处理函数
  * @note  定时器1用于生成舵机控制的PWM信号
  *        更新中断在定时器计数器溢出时触发
  *        频率：50Hz（每20ms触发一次）
  */
void TIM1_UP_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_UP_IRQn 0 */
  // 用户代码：定时器1中断处理开始部分（当前为空）
  // 可在此处添加PWM周期开始时的处理代码
  /* USER CODE END TIM1_UP_IRQn 0 */

  HAL_TIM_IRQHandler(&htim1);        // 调用HAL库的定时器中断处理函数

  /* USER CODE BEGIN TIM1_UP_IRQn 1 */
  // 用户代码：定时器1中断处理结束部分（当前为空）
  // 可在此处添加PWM周期结束时的处理代码
  /* USER CODE END TIM1_UP_IRQn 1 */
}

/* USER CODE BEGIN 1 */
// 用户自定义中断处理函数区域（当前为空）
// 可在此处添加其他外设的中断处理函数
/* USER CODE END 1 */

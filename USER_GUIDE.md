# STM32F103C8T6 智能垃圾桶 - 用户指南

## 快速开始

### 1. 硬件准备
- STM32F103C8T6开发板（蓝丸板）
- HC-SR501人体感应模块
- SG90舵机（90度）
- HC-SR04超声波测距模块
- 蜂鸣器模块
- LED灯（可选，板载LED可用）
- 杜邦线若干
- 面包板或PCB板

### 2. 软件环境
- Keil MDK-ARM 5.x
- STM32CubeProgrammer（用于烧录）
- ST-Link驱动程序

### 3. 连接硬件
按照 `circuit_connections.txt` 文件中的说明连接所有模块。

## 编译和烧录

### 方法一：使用Keil5
1. 打开Keil5 IDE
2. 打开项目文件：`smart_trash_can.uvprojx`
3. 编译项目：按F7或点击Build按钮
4. 连接ST-Link调试器
5. 下载程序：按F8或点击Download按钮

### 方法二：使用命令行（需要ARM GCC工具链）
```bash
# 编译项目
make

# 清理编译文件
make clean
```

## 功能说明

### 主要功能
1. **自动开盖**：检测到人体接近时自动打开垃圾桶盖
2. **垃圾满检测**：实时监测垃圾桶内垃圾高度
3. **声光报警**：垃圾满时LED亮起并蜂鸣器响起

### 工作流程
```
开机 → 初始化 → 主循环
                   ↓
              检测人体感应
                   ↓
              是否有人接近？
                   ↓
              开盖（5秒后自动关闭）
                   ↓
              检测垃圾高度
                   ↓
              垃圾是否满？
                   ↓
              触发声光报警
```

## 参数配置

### 可调参数（在main.c中修改）
```c
#define TRASH_FULL_DISTANCE 10    // 垃圾满检测距离(cm)
#define LID_OPEN_TIME 5000        // 开盖持续时间(ms)
#define SERVO_MIN_PULSE 500       // 舵机最小脉宽(us)
#define SERVO_MAX_PULSE 2500      // 舵机最大脉宽(us)
```

### 舵机角度调整
```c
// 修改舵机开盖和关盖角度
void Open_Lid(void)
{
    Servo_SetAngle(90);  // 开盖角度，可调整为0-180
}

void Close_Lid(void)
{
    Servo_SetAngle(0);   // 关盖角度，可调整为0-180
}
```

## 测试和调试

### 使用测试程序
1. 将 `test_modules.c` 中的测试函数添加到main.c
2. 在main()函数中调用相应的测试函数
3. 通过串口输出查看测试结果

### 常见测试
```c
// 测试所有模块
Test_All_Modules();

// 测试单个模块
Test_PIR_Sensor();        // 人体感应
Test_Servo_Motor();       // 舵机
Test_Ultrasonic_Sensor(); // 超声波
Test_LED_Buzzer();        // LED和蜂鸣器
```

## 故障排除

### 1. 编译错误
- **错误**：找不到头文件
- **解决**：检查Include路径设置，确保HAL库路径正确

- **错误**：链接错误
- **解决**：检查启动文件和链接脚本是否正确

### 2. 硬件问题
- **舵机不动作**
  - 检查5V电源是否充足
  - 检查PWM信号连接
  - 验证舵机是否损坏

- **超声波测距异常**
  - 检查Trig和Echo引脚连接
  - 确认5V电源供应
  - 检查测试环境（避免干扰）

- **人体感应不工作**
  - 调整HC-SR501的灵敏度旋钮
  - 检查电源连接
  - 确认安装位置和角度

### 3. 软件问题
- **程序不运行**
  - 检查时钟配置
  - 验证复位电路
  - 确认程序正确烧录

- **功能异常**
  - 检查GPIO配置
  - 验证定时器设置
  - 查看中断配置

## 扩展功能

### 1. 添加串口调试
```c
// 在main.c中添加UART初始化
// 实现printf重定向
int _write(int file, char *ptr, int len)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)ptr, len, 1000);
    return len;
}
```

### 2. 添加WiFi功能
- 使用ESP8266模块
- 实现远程监控和控制
- 发送垃圾满溢通知

### 3. 添加显示屏
- 使用OLED显示屏
- 显示垃圾桶状态
- 显示距离和时间信息

### 4. 电池供电
- 添加电池管理电路
- 实现低功耗模式
- 电量监测功能

## 维护和保养

### 定期检查
1. **每周检查**
   - 清洁超声波传感器表面
   - 检查舵机动作是否顺畅
   - 验证各连接线是否牢固

2. **每月检查**
   - 检查电源供应稳定性
   - 清理灰尘和杂物
   - 测试所有功能是否正常

### 软件更新
1. 备份当前程序
2. 测试新功能
3. 逐步部署更新

## 安全注意事项

1. **电气安全**
   - 确保正确接地
   - 避免短路
   - 使用合适的保险丝

2. **机械安全**
   - 舵机动作时注意安全
   - 避免夹伤手指
   - 确保安装牢固

3. **环境安全**
   - 防水防潮
   - 避免高温环境
   - 定期清洁维护

## 技术支持

### 文档参考
- STM32F103C8T6数据手册
- HAL库用户手册
- 各传感器模块说明书

### 在线资源
- STM32官方文档
- 社区论坛
- 开源项目参考

### 联系方式
如有技术问题，请参考项目README.md文件或相关技术文档。

/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : 智能垃圾桶主程序文件
  * @description    : 基于STM32F103C8T6的智能垃圾桶控制系统
  *                   实现自动开盖、垃圾满检测、声光报警等功能
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"                    // 包含主头文件，定义了GPIO引脚和函数声明

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
// 用户自定义头文件包含区域（当前为空）
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
// 用户自定义类型定义区域（当前为空）
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
// 舵机控制相关宏定义
#define SERVO_MIN_PULSE 500   // 舵机最小脉宽：0.5ms对应0度角
#define SERVO_MAX_PULSE 2500  // 舵机最大脉宽：2.5ms对应180度角
#define SERVO_PERIOD 20000    // 舵机PWM周期：20ms（50Hz频率）

// 超声波传感器相关宏定义
#define ULTRASONIC_TIMEOUT 30000  // 超声波测距超时时间：30ms
#define TRASH_FULL_DISTANCE 10    // 垃圾满检测距离：10cm（小于此距离认为垃圾满）
#define LID_OPEN_TIME 5000        // 垃圾桶盖保持打开时间：5秒

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
// 用户自定义宏定义区域（当前为空）
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
// 定时器句柄结构体，用于控制硬件定时器
TIM_HandleTypeDef htim1;              // 定时器1句柄，用于舵机PWM控制
TIM_HandleTypeDef htim2;              // 定时器2句柄，用于通用定时功能

/* USER CODE BEGIN PV */
// 全局变量定义，用于系统状态管理
uint32_t pir_last_trigger = 0;       // PIR传感器最后触发时间戳（毫秒）
uint32_t lid_open_time = 0;           // 垃圾桶盖打开时间戳（毫秒）
uint8_t lid_is_open = 0;              // 垃圾桶盖状态标志：0=关闭，1=打开
uint8_t trash_full_alarm = 0;         // 垃圾满报警状态标志：0=正常，1=报警

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
// 系统配置函数声明
void SystemClock_Config(void);        // 系统时钟配置函数
static void MX_GPIO_Init(void);       // GPIO初始化函数
static void MX_TIM1_Init(void);       // 定时器1初始化函数（舵机PWM）
static void MX_TIM2_Init(void);       // 定时器2初始化函数

/* USER CODE BEGIN PFP */
// 用户自定义函数声明
void Servo_SetAngle(uint16_t angle);  // 设置舵机角度函数
uint32_t Ultrasonic_GetDistance(void);// 获取超声波测距结果函数
void Open_Lid(void);                  // 打开垃圾桶盖函数
void Close_Lid(void);                 // 关闭垃圾桶盖函数
void Alarm_On(void);                  // 开启报警（LED+蜂鸣器）函数
void Alarm_Off(void);                 // 关闭报警函数
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
  * @brief  设置舵机角度（0-180度）
  * @param  angle: 目标角度值（0-180度）
  * @retval None
  * @note   通过PWM脉宽控制舵机角度：
  *         - 0度对应0.5ms脉宽
  *         - 90度对应1.5ms脉宽
  *         - 180度对应2.5ms脉宽
  *         - PWM频率为50Hz（周期20ms）
  */
void Servo_SetAngle(uint16_t angle)
{
    // 角度限制：确保输入角度不超过180度
    if (angle > 180) angle = 180;

    // 计算PWM脉宽：线性插值计算对应角度的脉宽值
    // 公式：脉宽 = 最小脉宽 + (角度/180) * (最大脉宽 - 最小脉宽)
    uint32_t pulse_width = SERVO_MIN_PULSE + (angle * (SERVO_MAX_PULSE - SERVO_MIN_PULSE)) / 180;

    // 设置定时器1通道1的比较值，控制PWM脉宽
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pulse_width);
}

/**
  * @brief  获取超声波传感器测距结果
  * @retval 距离值（单位：厘米），返回0表示测距失败
  * @note   HC-SR04超声波测距原理：
  *         1. 发送10us高电平触发脉冲到Trig引脚
  *         2. 传感器发射40KHz超声波并等待回波
  *         3. Echo引脚输出高电平，持续时间等于声波往返时间
  *         4. 根据时间计算距离：距离 = (时间 × 声速) / 2
  */
uint32_t Ultrasonic_GetDistance(void)
{
    uint32_t start_time, end_time, duration;  // 时间变量：开始时间、结束时间、持续时间
    uint32_t timeout_counter = 0;             // 超时计数器，防止程序死锁

    // 第一步：发送触发脉冲（10微秒高电平）
    HAL_GPIO_WritePin(ULTRASONIC_TRIG_GPIO_Port, ULTRASONIC_TRIG_Pin, GPIO_PIN_SET);
    // 精确延时约10微秒（在72MHz主频下，每个循环约1/72微秒）
    for(volatile int i = 0; i < 72; i++); // 72个循环 ≈ 10us
    HAL_GPIO_WritePin(ULTRASONIC_TRIG_GPIO_Port, ULTRASONIC_TRIG_Pin, GPIO_PIN_RESET);

    // 第二步：等待Echo引脚变为高电平（表示开始接收回波）
    timeout_counter = 0;
    while (HAL_GPIO_ReadPin(ULTRASONIC_ECHO_GPIO_Port, ULTRASONIC_ECHO_Pin) == GPIO_PIN_RESET)
    {
        timeout_counter++;                    // 增加超时计数
        if (timeout_counter > 10000) return 0; // 超时保护：避免无限等待
    }

    // 第三步：记录Echo引脚变高的时间点
    start_time = HAL_GetTick();              // 获取当前系统时间戳（毫秒）

    // 第四步：等待Echo引脚变为低电平（表示回波接收结束）
    timeout_counter = 0;
    while (HAL_GPIO_ReadPin(ULTRASONIC_ECHO_GPIO_Port, ULTRASONIC_ECHO_Pin) == GPIO_PIN_SET)
    {
        timeout_counter++;                    // 增加超时计数
        if (timeout_counter > 30000) return 0; // 超时保护：避免无限等待
    }
    end_time = HAL_GetTick();                // 获取Echo变低的时间戳

    // 第五步：计算Echo高电平持续时间
    duration = end_time - start_time;        // 计算时间差（毫秒）

    // 第六步：根据声波传播时间计算距离
    // 声速 = 343 m/s = 34300 cm/s = 34.3 cm/ms
    // 距离 = (往返时间 × 声速) / 2
    // 对于毫秒级时间：距离 = (duration_ms × 34.3) / 2 ≈ duration_ms × 17
    if (duration == 0) return 0;            // 防止除零错误

    return (duration * 17);                  // 返回近似距离值（厘米）
}

/**
  * @brief  打开垃圾桶盖
  * @retval None
  * @note   通过控制舵机转动到90度位置来打开垃圾桶盖
  *         同时更新系统状态变量
  */
void Open_Lid(void)
{
    Servo_SetAngle(90);                      // 设置舵机角度为90度（打开位置）
    lid_is_open = 1;                         // 更新盖子状态标志为"已打开"
    lid_open_time = HAL_GetTick();           // 记录打开时间戳，用于自动关闭计时
}

/**
  * @brief  关闭垃圾桶盖
  * @retval None
  * @note   通过控制舵机转动到0度位置来关闭垃圾桶盖
  *         同时更新系统状态变量
  */
void Close_Lid(void)
{
    Servo_SetAngle(0);                       // 设置舵机角度为0度（关闭位置）
    lid_is_open = 0;                         // 更新盖子状态标志为"已关闭"
}

/**
  * @brief  开启报警（LED灯和蜂鸣器）
  * @retval None
  * @note   当检测到垃圾满溢时调用此函数
  *         LED使用低电平点亮（板载LED特性）
  *         蜂鸣器使用高电平驱动
  */
void Alarm_On(void)
{
    // 点亮LED灯：PC13引脚输出低电平（板载LED为低电平有效）
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);
    // 开启蜂鸣器：PA1引脚输出高电平
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_SET);
}

/**
  * @brief  关闭报警（LED灯和蜂鸣器）
  * @retval None
  * @note   当垃圾清理完成后调用此函数关闭报警
  */
void Alarm_Off(void)
{
    // 熄灭LED灯：PC13引脚输出高电平
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);
    // 关闭蜂鸣器：PA1引脚输出低电平
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_RESET);
}

/* USER CODE END 0 */

/**
  * @brief  应用程序入口点 - 智能垃圾桶主函数
  * @retval int 程序返回值（实际上不会返回）
  * @note   主函数执行流程：
  *         1. 系统初始化（HAL库、时钟、外设）
  *         2. 硬件模块初始化（GPIO、定时器、PWM）
  *         3. 进入主循环（人体检测、距离检测、控制逻辑）
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
  // 用户代码区域1：主函数开始前的代码（当前为空）
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/
  // 微控制器配置部分

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  // 初始化HAL库：复位所有外设，初始化Flash接口和系统滴答定时器
  HAL_Init();

  /* USER CODE BEGIN Init */
  // 用户代码区域：初始化阶段的自定义代码（当前为空）
  /* USER CODE END Init */

  /* Configure the system clock */
  // 配置系统时钟：设置主频为72MHz
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
  // 用户代码区域：系统初始化阶段的自定义代码（当前为空）
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  // 初始化所有配置的外设模块
  MX_GPIO_Init();                              // 初始化GPIO引脚配置
  MX_TIM1_Init();                              // 初始化定时器1（用于舵机PWM）
  MX_TIM2_Init();                              // 初始化定时器2（通用定时器）

  /* USER CODE BEGIN 2 */
  // 用户代码区域2：外设初始化完成后的代码

  // 启动定时器1的PWM输出，用于控制舵机
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);

  // 初始化舵机到关闭位置（0度）
  Close_Lid();

  // 初始化时关闭报警（LED和蜂鸣器）
  Alarm_Off();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  // 主循环：系统核心控制逻辑
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // ==================== 人体感应检测模块 ====================
    // 检测PIR传感器状态，判断是否有人接近垃圾桶
    if (HAL_GPIO_ReadPin(PIR_SENSOR_GPIO_Port, PIR_SENSOR_Pin) == GPIO_PIN_SET)
    {
        // 检测到人体接近时的处理逻辑
        // 条件：垃圾桶盖未打开 且 没有垃圾满报警
        if (!lid_is_open && !trash_full_alarm)
        {
            Open_Lid();                          // 自动打开垃圾桶盖
        }
        pir_last_trigger = HAL_GetTick();        // 更新最后触发时间戳
    }

    // ==================== 自动关盖模块 ====================
    // 检查垃圾桶盖是否需要自动关闭（超时关闭机制）
    if (lid_is_open && (HAL_GetTick() - lid_open_time) > LID_OPEN_TIME)
    {
        Close_Lid();                             // 超时后自动关闭垃圾桶盖
    }

    // ==================== 垃圾满溢检测模块 ====================
    // 定时检测垃圾桶内垃圾高度（每2秒检测一次）
    static uint32_t last_distance_check = 0;    // 静态变量：上次检测时间
    if ((HAL_GetTick() - last_distance_check) > 2000)
    {
        uint32_t distance = Ultrasonic_GetDistance(); // 获取当前距离值

        // 垃圾满检测逻辑：距离小于阈值且测量有效
        if (distance > 0 && distance < TRASH_FULL_DISTANCE)
        {
            // 首次检测到垃圾满的处理
            if (!trash_full_alarm)
            {
                trash_full_alarm = 1;            // 设置垃圾满报警标志
                Alarm_On();                      // 开启声光报警
                Close_Lid();                     // 强制关闭垃圾桶盖
            }
        }
        // 垃圾清理检测逻辑：距离大于阈值+滞回值（防止误触发）
        else if (distance >= TRASH_FULL_DISTANCE + 5)
        {
            // 检测到垃圾已清理的处理
            if (trash_full_alarm)
            {
                trash_full_alarm = 0;            // 清除垃圾满报警标志
                Alarm_Off();                     // 关闭声光报警
            }
        }

        last_distance_check = HAL_GetTick();     // 更新检测时间戳
    }

    // ==================== 主循环延时 ====================
    HAL_Delay(100);                              // 主循环延时100ms，降低CPU占用率
  }
  /* USER CODE END 3 */
}

/**
  * @brief 系统时钟配置函数
  * @retval None
  * @note  配置系统时钟为72MHz：
  *        - 外部晶振(HSE): 8MHz
  *        - PLL倍频: 9倍 (8MHz × 9 = 72MHz)
  *        - AHB时钟: 72MHz (系统时钟)
  *        - APB1时钟: 36MHz (AHB/2，最大36MHz)
  *        - APB2时钟: 72MHz (AHB/1，最大72MHz)
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};  // 振荡器配置结构体
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};  // 时钟配置结构体

  /** 初始化RCC振荡器配置
   * 配置外部高速晶振(HSE)和锁相环(PLL)
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;  // 使用外部高速晶振
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;                    // 开启HSE
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;     // HSE预分频器：不分频
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;                    // 保持内部高速振荡器开启
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;                // 开启PLL
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;        // PLL时钟源选择HSE
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;               // PLL倍频系数：9倍

  // 应用振荡器配置，如果配置失败则调用错误处理函数
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();  // 配置失败，进入错误处理
  }

  /** 初始化CPU、AHB和APB总线时钟配置
   * 设置各个总线的时钟分频比例
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;   // 系统时钟源选择PLL
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;         // AHB时钟 = 系统时钟/1 = 72MHz
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;          // APB1时钟 = AHB时钟/2 = 36MHz
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;          // APB2时钟 = AHB时钟/1 = 72MHz

  // 应用时钟配置，设置Flash延迟为2个等待周期（72MHz需要）
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();  // 配置失败，进入错误处理
  }
}

/**
  * @brief TIM1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM1_Init(void)
{
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};
  TIM_OC_InitTypeDef sConfigOC = {0};
  TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};

  htim1.Instance = TIM1;
  htim1.Init.Prescaler = 71;  // 72MHz / 72 = 1MHz
  htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim1.Init.Period = 19999;  // 20ms period for servo (50Hz)
  htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim1.Init.RepetitionCounter = 0;
  htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim1) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim1, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_Init(&htim1) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim1, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sConfigOC.OCMode = TIM_OCMODE_PWM1;
  sConfigOC.Pulse = 1500;  // Initial pulse width (1.5ms = 90 degrees)
  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
  sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
  sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
  sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
  if (HAL_TIM_PWM_ConfigChannel(&htim1, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
  {
    Error_Handler();
  }
  sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_DISABLE;
  sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_DISABLE;
  sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
  sBreakDeadTimeConfig.DeadTime = 0;
  sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
  sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_HIGH;
  sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
  if (HAL_TIMEx_ConfigBreakDeadTime(&htim1, &sBreakDeadTimeConfig) != HAL_OK)
  {
    Error_Handler();
  }

  HAL_TIM_MspPostInit(&htim1);
}

/**
  * @brief TIM2 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM2_Init(void)
{
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};

  htim2.Instance = TIM2;
  htim2.Init.Prescaler = 71;
  htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim2.Init.Period = 65535;
  htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim2) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim2, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim2, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, LED_Pin, GPIO_PIN_SET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, BUZZER_Pin, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(ULTRASONIC_TRIG_GPIO_Port, ULTRASONIC_TRIG_Pin, GPIO_PIN_RESET);

  /*Configure GPIO pin : LED_Pin */
  GPIO_InitStruct.Pin = LED_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(LED_GPIO_Port, &GPIO_InitStruct);

  /*Configure GPIO pins : PIR_SENSOR_Pin ULTRASONIC_ECHO_Pin */
  GPIO_InitStruct.Pin = PIR_SENSOR_Pin|ULTRASONIC_ECHO_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : BUZZER_Pin */
  GPIO_InitStruct.Pin = BUZZER_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(BUZZER_GPIO_Port, &GPIO_InitStruct);

  /*Configure GPIO pin : ULTRASONIC_TRIG_Pin */
  GPIO_InitStruct.Pin = ULTRASONIC_TRIG_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(ULTRASONIC_TRIG_GPIO_Port, &GPIO_InitStruct);
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

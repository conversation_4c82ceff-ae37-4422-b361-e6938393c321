Dependencies for Project 'smart_trash_can', Target 'smart_trash_can': (DO NOT MODIFY !)
CompilerVersion: 5060750::V5.06 update 6 (build 750)::.\ARMCC
F (.\Core\Src\main.c)(0x6873AE58)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\Core\Inc\main.h)(0x6873AC55)
F (.\Core\Src\stm32f1xx_it.c)(0x6873ACEC)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_it.o --omf_browse .\objects\stm32f1xx_it.crf --depend .\objects\stm32f1xx_it.d)
I (.\Core\Inc\main.h)(0x6873AC55)
F (.\Core\Src\stm32f1xx_hal_msp.c)(0x6873AD01)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_msp.o --omf_browse .\objects\stm32f1xx_hal_msp.crf --depend .\objects\stm32f1xx_hal_msp.d)
I (.\Core\Inc\main.h)(0x6873AC55)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_gpio_ex.o --omf_browse .\objects\stm32f1xx_hal_gpio_ex.crf --depend .\objects\stm32f1xx_hal_gpio_ex.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_tim.o --omf_browse .\objects\stm32f1xx_hal_tim.crf --depend .\objects\stm32f1xx_hal_tim.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_tim_ex.o --omf_browse .\objects\stm32f1xx_hal_tim_ex.crf --depend .\objects\stm32f1xx_hal_tim_ex.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal.o --omf_browse .\objects\stm32f1xx_hal.crf --depend .\objects\stm32f1xx_hal.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_rcc.o --omf_browse .\objects\stm32f1xx_hal_rcc.crf --depend .\objects\stm32f1xx_hal_rcc.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_rcc_ex.o --omf_browse .\objects\stm32f1xx_hal_rcc_ex.crf --depend .\objects\stm32f1xx_hal_rcc_ex.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_gpio.o --omf_browse .\objects\stm32f1xx_hal_gpio.crf --depend .\objects\stm32f1xx_hal_gpio.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_dma.o --omf_browse .\objects\stm32f1xx_hal_dma.crf --depend .\objects\stm32f1xx_hal_dma.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_cortex.o --omf_browse .\objects\stm32f1xx_hal_cortex.crf --depend .\objects\stm32f1xx_hal_cortex.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_pwr.o --omf_browse .\objects\stm32f1xx_hal_pwr.crf --depend .\objects\stm32f1xx_hal_pwr.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_flash.o --omf_browse .\objects\stm32f1xx_hal_flash.crf --depend .\objects\stm32f1xx_hal_flash.d)
F (.\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_hal_flash_ex.o --omf_browse .\objects\stm32f1xx_hal_flash_ex.crf --depend .\objects\stm32f1xx_hal_flash_ex.d)
F (.\Core\Src\system_stm32f1xx.c)(0x6873AD3F)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\system_stm32f1xx.o --omf_browse .\objects\system_stm32f1xx.crf --depend .\objects\system_stm32f1xx.d)
F (RTE/Device/STM32F103C8/RTE_Device.h)(0x52431878)()
F (RTE/Device/STM32F103C8/startup_stm32f10x_md.s)(0x4F1E2E3C)(--cpu Cortex-M3 -g --apcs=interwork 

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

--pd "__UVISION_VERSION SETA 537" --pd "_RTE_ SETA 1" --pd "STM32F10X_MD SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_stm32f10x_md.lst --xref -o .\objects\startup_stm32f10x_md.o --depend .\objects\startup_stm32f10x_md.d)
F (RTE/Device/STM32F103C8/system_stm32f10x.c)(0x4F1E2E3C)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Core\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\Device\STM32F103C8

-I.\RTE\_smart_trash_can

-IC:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-IC:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include

-D__UVISION_VERSION="537" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\system_stm32f10x.o --omf_browse .\objects\system_stm32f10x.crf --depend .\objects\system_stm32f10x.d)
I (C:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include\stm32f10x.h)(0x528979E6)
I (C:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include\core_cm3.h)(0x626F4ADE)
I (C:\keil5\ARM\ARMCC\include\stdint.h)(0x5EC775FC)
I (C:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_version.h)(0x626F4ADE)
I (C:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_compiler.h)(0x626F4ADE)
I (C:\keil5\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_armcc.h)(0x626F4ADE)
I (C:\keil5\Keil\STM32F1xx_DFP\1.0.5\Device\Include\system_stm32f10x.h)(0x4F1E2E3C)

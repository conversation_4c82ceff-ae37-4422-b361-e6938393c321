/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32f1xx_it.h
  * @brief   智能垃圾桶中断处理函数头文件
  * @description 包含系统中断和外设中断处理函数的声明
  *              定义了Cortex-M3内核异常和STM32外设中断的处理接口
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __STM32F1xx_IT_H            // 防止头文件重复包含
#define __STM32F1xx_IT_H

#ifdef __cplusplus
 extern "C" {
#endif

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
// 用户自定义头文件包含区域（当前为空）
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
// 导出类型定义区域（当前为空）
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */
// 导出常量定义区域（当前为空）
/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */
// 导出宏定义区域（当前为空）
/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
// Cortex-M3内核异常处理函数声明
void NMI_Handler(void);              // 不可屏蔽中断处理函数
void HardFault_Handler(void);        // 硬件错误中断处理函数
void MemManage_Handler(void);        // 内存管理错误中断处理函数
void BusFault_Handler(void);         // 总线错误中断处理函数
void UsageFault_Handler(void);       // 用法错误中断处理函数
void SVC_Handler(void);              // 系统服务调用中断处理函数
void DebugMon_Handler(void);         // 调试监视器中断处理函数
void PendSV_Handler(void);           // 可挂起系统调用中断处理函数
void SysTick_Handler(void);          // 系统滴答定时器中断处理函数

// STM32外设中断处理函数声明
void TIM1_UP_IRQHandler(void);       // 定时器1更新中断处理函数（用于舵机PWM）

/* USER CODE BEGIN EFP */
// 用户自定义函数声明区域（当前为空）
/* USER CODE END EFP */

#ifdef __cplusplus
}
#endif

#endif /* __STM32F1xx_IT_H */

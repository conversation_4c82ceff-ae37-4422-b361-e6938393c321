# STM32F103C8T6 智能垃圾桶项目

## 项目概述
基于STM32F103C8T6微控制器的智能垃圾桶系统，使用HAL库实现以下功能：

1. **自动开盖功能** - 检测到人靠近时，舵机控制桶盖打开
2. **垃圾满溢检测** - 超声波测距检测垃圾高度
3. **声光报警** - 垃圾满时LED灯亮起并蜂鸣器响起

## 硬件连接

### 引脚分配
- **PA0** - HC-SR501人体感应模块输入
- **PA1** - 蜂鸣器控制输出
- **PA8** - 90度舵机PWM控制输出
- **PA10** - HC-SR04超声波模块Echo输入
- **PB0** - HC-SR04超声波模块Trig输出
- **PC13** - LED指示灯输出（板载LED）

### 外设模块
1. **HC-SR501人体感应模块** - 检测人体接近
2. **90度舵机** - 控制垃圾桶盖开关
3. **HC-SR04超声波测距模块** - 检测垃圾桶内垃圾高度
4. **LED灯** - 垃圾满溢指示
5. **蜂鸣器** - 垃圾满溢声音报警

## 功能说明

### 自动开盖
- 当HC-SR501检测到人体接近时，舵机驱动垃圾桶盖打开90度
- 桶盖保持打开状态5秒后自动关闭
- 如果垃圾已满，则不会自动开盖

### 垃圾满溢检测
- 每2秒检测一次垃圾桶内垃圾高度
- 当检测距离小于10cm时，判定为垃圾满溢
- 垃圾满溢时触发声光报警并强制关闭桶盖

### 声光报警
- 垃圾满溢时LED灯亮起（PC13低电平有效）
- 同时蜂鸣器响起进行声音提醒
- 当垃圾清理后（距离大于15cm），自动解除报警

## 编译和烧录

### 开发环境
- **IDE**: Keil MDK-ARM 5
- **芯片**: STM32F103C8T6
- **库**: STM32 HAL库

### 编译步骤
1. 打开Keil5
2. 打开项目文件 `smart_trash_can.uvprojx`
3. 编译项目（F7或Build按钮）
4. 生成hex文件用于烧录

### 烧录方法
- 使用ST-Link或J-Link等调试器
- 通过SWD接口连接STM32F103C8T6
- 烧录生成的hex文件到Flash

## 系统配置

### 时钟配置
- 外部晶振：8MHz
- 系统时钟：72MHz（通过PLL倍频）
- APB1时钟：36MHz
- APB2时钟：72MHz

### 定时器配置
- **TIM1**: PWM输出，用于舵机控制（50Hz，1-2ms脉宽）
- **TIM2**: 通用定时器，用于超声波测距时序

## 注意事项

1. **电源要求**: 确保5V电源供应充足，舵机需要较大电流
2. **接线检查**: 仔细检查各模块接线，避免短路
3. **调试**: 可通过串口输出调试信息（需要添加UART配置）
4. **安全**: 舵机动作时注意安全，避免夹伤

## 文件结构
```
├── Core/
│   ├── Inc/
│   │   ├── main.h
│   │   ├── stm32f1xx_hal_conf.h
│   │   └── stm32f1xx_it.h
│   └── Src/
│       ├── main.c
│       ├── stm32f1xx_it.c
│       ├── stm32f1xx_hal_msp.c
│       └── system_stm32f1xx.c
├── startup_stm32f103c8tx.s
├── smart_trash_can.uvprojx
└── README.md
```

## 版本信息
- **版本**: v1.0
- **日期**: 2024
- **作者**: STM32 HAL库实现

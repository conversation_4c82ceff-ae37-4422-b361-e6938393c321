STM32F103C8T6 智能垃圾桶 - 电路连接图

===========================================
STM32F103C8T6 引脚连接说明
===========================================

主控芯片: STM32F103C8T6 (48引脚)

电源连接:
---------
VDD (引脚1, 32, 48) -> 3.3V
VSS (引脚16, 31, 47) -> GND
VDDA (引脚9) -> 3.3V (模拟电源)
VSSA (引脚8) -> GND (模拟地)

时钟连接:
---------
OSC_IN (引脚5) -> 8MHz晶振
OSC_OUT (引脚6) -> 8MHz晶振
晶振两端各接22pF电容到地

调试接口:
---------
SWD<PERSON> (引脚34/PA13) -> ST-Link SWDIO
SWCLK (引脚37/PA14) -> ST-Link SWCLK

===========================================
外设模块连接
===========================================

1. HC-SR501 人体感应模块
------------------------
VCC -> 5V (或3.3V，根据模块规格)
GND -> GND
OUT -> PA0 (引脚10)

连接说明:
- 检测范围: 3-7米
- 检测角度: 约120度
- 延时时间: 可调(0.3秒-18秒)
- 封锁时间: 可调(0.2秒-80秒)

2. 90度舵机 (SG90或类似)
-----------------------
红线(VCC) -> 5V
棕线(GND) -> GND
橙线(信号) -> PA8 (引脚29)

连接说明:
- 工作电压: 4.8V-6V
- 控制信号: PWM (50Hz, 1-2ms脉宽)
- 0度: 1ms脉宽
- 90度: 1.5ms脉宽
- 180度: 2ms脉宽

3. HC-SR04 超声波测距模块
------------------------
VCC -> 5V
GND -> GND
Trig -> PB0 (引脚18)
Echo -> PA10 (引脚31)

连接说明:
- 测距范围: 2cm-400cm
- 精度: 3mm
- 测量角度: 15度
- 工作频率: 40KHz

4. LED指示灯
-----------
正极 -> PA13 (引脚34) 通过220Ω电阻
负极 -> GND

注意: STM32F103C8T6板载LED通常在PC13
如使用板载LED:
PC13 (引脚2) -> 板载LED (低电平点亮)

5. 蜂鸣器模块
-----------
VCC -> 3.3V (或5V)
GND -> GND
I/O -> PA1 (引脚11)

连接说明:
- 有源蜂鸣器: 直接高电平驱动
- 无源蜂鸣器: 需要PWM信号驱动

===========================================
电路保护和注意事项
===========================================

1. 电源滤波:
- 在VDD和VSS之间接100nF陶瓷电容
- 在电源输入端接10uF电解电容

2. 复位电路:
- NRST (引脚7) 接10kΩ上拉电阻到3.3V
- 可选: 接复位按钮到GND

3. 电流保护:
- 舵机工作电流较大(500mA-1A)，建议单独供电
- 可使用继电器或MOSFET控制舵机电源

4. 信号完整性:
- 长线连接建议加上拉/下拉电阻
- 数字信号线可加串联电阻(100Ω-470Ω)

===========================================
PCB布局建议
===========================================

1. 电源布线:
- 电源线尽量粗，减少压降
- 数字地和模拟地在单点连接

2. 信号布线:
- 时钟信号远离敏感信号
- 高频信号线尽量短

3. 去耦电容:
- 每个电源引脚附近放置去耦电容
- 高频去耦电容靠近芯片放置

4. 热设计:
- 舵机和电源芯片注意散热
- 避免热敏感器件靠近热源

===========================================
测试点建议
===========================================

1. 电源测试点:
- 3.3V, 5V, GND

2. 信号测试点:
- PA0 (PIR信号)
- PA8 (舵机PWM)
- PB0 (超声波Trig)
- PA10 (超声波Echo)
- PA1 (蜂鸣器)
- PC13 (LED)

3. 调试接口:
- SWDIO, SWCLK, GND, 3.3V

===========================================
故障排除
===========================================

1. 舵机不动作:
- 检查5V电源是否充足
- 检查PWM信号是否正确
- 检查舵机连线

2. 超声波测距异常:
- 检查5V电源
- 检查Trig和Echo连线
- 确认无障碍物干扰

3. 人体感应不工作:
- 检查电源连接
- 调整感应距离和延时
- 确认安装位置合适

4. LED/蜂鸣器不工作:
- 检查GPIO配置
- 检查电源和地连接
- 确认电流限制电阻值

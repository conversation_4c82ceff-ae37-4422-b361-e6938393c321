/**
  ******************************************************************************
  * @file    test_modules.c
  * @brief   智能垃圾桶模块测试函数集合
  * <AUTHOR> HAL Implementation
  * @date    2024
  * @description 提供各个硬件模块的独立测试函数和综合测试功能
  *              用于验证PIR传感器、舵机、超声波传感器、LED和蜂鸣器的工作状态
  ******************************************************************************
  * @attention
  *
  * 此文件包含各个模块的测试函数，使用方法：
  * 1. 将此文件中的测试函数复制到main.c中
  * 2. 在main()函数中调用所需的测试函数
  * 3. 通过串口输出或LED指示观察测试结果
  * 4. 建议逐个测试各模块，确保硬件连接正确
  *
  ******************************************************************************
  */

#include "main.h"                    // 包含主头文件，获取GPIO定义和函数声明

/* External variables */
extern TIM_HandleTypeDef htim1;      // 外部定时器1句柄（用于舵机PWM控制）

/* Test function prototypes */
// 测试函数声明 - 各模块独立测试
void Test_PIR_Sensor(void);          // PIR人体感应传感器测试
void Test_Servo_Motor(void);         // 舵机动作测试
void Test_Ultrasonic_Sensor(void);   // 超声波测距传感器测试
void Test_LED_Buzzer(void);          // LED灯和蜂鸣器测试
void Test_All_Modules(void);         // 所有模块综合测试

/**
  * @brief  PIR人体感应传感器功能测试
  * @retval None
  * @note   测试HC-SR501人体感应模块的工作状态
  *         - 检测到人体时：串口输出提示信息，LED灯亮起
  *         - 无人体时：串口输出无运动信息，LED灯熄灭
  *         - 测试周期：500ms检测一次
  * @usage  将此函数在main()中调用，通过串口监视器观察输出
  */
void Test_PIR_Sensor(void)
{
    printf("开始测试PIR人体感应传感器...\n");
    printf("请在传感器前方移动以触发检测\n");

    // 无限循环进行PIR传感器测试
    while(1)
    {
        // 读取PIR传感器状态（PA0引脚）
        if (HAL_GPIO_ReadPin(PIR_SENSOR_GPIO_Port, PIR_SENSOR_Pin) == GPIO_PIN_SET)
        {
            // 检测到人体运动
            printf("检测到人体运动！\n");
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET); // 点亮LED（低电平有效）
            HAL_Delay(1000);                                           // 保持1秒指示状态
        }
        else
        {
            // 未检测到人体运动
            printf("无人体运动\n");
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);   // 熄灭LED
        }
        HAL_Delay(500);                                                // 每500ms检测一次
    }
}

/**
  * @brief  舵机功能测试
  * @retval None
  * @note   测试90度舵机的角度控制功能
  *         - 依次转动到0°、45°、90°、135°、180°位置
  *         - 每个位置保持2秒，便于观察舵机动作
  *         - 通过PWM信号控制舵机角度
  * @usage  将此函数在main()中调用，观察舵机是否按预期角度转动
  * @warning 确保舵机电源充足（5V），否则可能无法正常工作
  */
void Test_Servo_Motor(void)
{
    printf("开始测试舵机功能...\n");
    printf("舵机将依次转动到不同角度位置\n");

    // 无限循环进行舵机角度测试
    while(1)
    {
        // 转动到0度位置（垃圾桶关闭位置）
        printf("舵机转动到0度位置\n");
        Servo_SetAngle(0);
        HAL_Delay(2000);                    // 保持2秒观察动作

        // 转动到45度位置
        printf("舵机转动到45度位置\n");
        Servo_SetAngle(45);
        HAL_Delay(2000);                    // 保持2秒观察动作

        // 转动到90度位置（垃圾桶打开位置）
        printf("舵机转动到90度位置\n");
        Servo_SetAngle(90);
        HAL_Delay(2000);                    // 保持2秒观察动作

        // 转动到135度位置
        printf("舵机转动到135度位置\n");
        Servo_SetAngle(135);
        HAL_Delay(2000);                    // 保持2秒观察动作

        // 转动到180度位置（最大角度）
        printf("舵机转动到180度位置\n");
        Servo_SetAngle(180);
        HAL_Delay(2000);                    // 保持2秒观察动作
    }
}

/**
  * @brief  超声波传感器功能测试
  * @retval None
  * @note   测试HC-SR04超声波测距模块的工作状态
  *         - 每秒测量一次距离并通过串口输出
  *         - 距离小于10cm时点亮LED警告（模拟垃圾满状态）
  *         - 距离大于10cm时熄灭LED
  *         - 测量失败时输出错误信息
  * @usage  将此函数在main()中调用，在传感器前放置不同距离的物体测试
  * @warning 确保传感器前方无遮挡，测试环境相对安静
  */
void Test_Ultrasonic_Sensor(void)
{
    uint32_t distance;                       // 存储测距结果的变量

    printf("开始测试超声波传感器...\n");
    printf("请在传感器前方放置物体进行测距\n");
    printf("距离阈值：10cm（小于此值将点亮LED）\n");

    // 无限循环进行超声波测距测试
    while(1)
    {
        // 调用超声波测距函数获取距离值
        distance = Ultrasonic_GetDistance();

        // 判断测距是否成功
        if (distance > 0)
        {
            // 测距成功，输出距离值
            printf("测量距离: %lu cm\n", distance);

            // 根据距离值控制LED状态（模拟垃圾满检测）
            if (distance < 10)
            {
                printf("物体过近！（模拟垃圾满状态）\n");
                HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET); // 点亮LED警告
            }
            else
            {
                HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);   // 熄灭LED
            }
        }
        else
        {
            // 测距失败，可能原因：超时、无回波、距离超出范围
            printf("测量错误或超出测量范围\n");
        }

        HAL_Delay(1000);                     // 每1秒测量一次
    }
}

/**
  * @brief  LED灯和蜂鸣器功能测试
  * @retval None
  * @note   测试LED指示灯和蜂鸣器的工作状态
  *         - LED和蜂鸣器同时开启1秒，然后关闭1秒
  *         - 循环进行，模拟垃圾满溢时的声光报警效果
  *         - LED使用低电平点亮（板载LED特性）
  *         - 蜂鸣器使用高电平驱动
  * @usage  将此函数在main()中调用，观察LED闪烁和听到蜂鸣器声音
  * @warning 测试时注意蜂鸣器音量，避免在安静环境中造成干扰
  */
void Test_LED_Buzzer(void)
{
    printf("开始测试LED和蜂鸣器...\n");
    printf("LED将闪烁，蜂鸣器将发出声音\n");

    // 无限循环进行LED和蜂鸣器测试
    while(1)
    {
        // 开启LED和蜂鸣器（模拟报警状态）
        printf("LED和蜂鸣器开启\n");
        HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);     // 点亮LED（低电平有效）
        HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_SET); // 开启蜂鸣器（高电平驱动）
        HAL_Delay(1000);                                               // 保持1秒

        // 关闭LED和蜂鸣器（模拟正常状态）
        printf("LED和蜂鸣器关闭\n");
        HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);       // 熄灭LED
        HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_RESET); // 关闭蜂鸣器
        HAL_Delay(1000);                                               // 保持1秒
    }
}

/**
  * @brief  所有模块综合测试
  * @retval None
  * @note   按顺序测试所有硬件模块的功能
  *         测试顺序：PIR传感器 → 超声波传感器 → 舵机 → LED和蜂鸣器
  *         每个测试周期约5秒，便于观察各模块工作状态
  *         通过串口输出详细的测试信息和结果
  * @usage  将此函数在main()中调用，进行系统整体功能验证
  * @note   这是推荐的测试方式，可以快速验证所有硬件是否正常工作
  */
void Test_All_Modules(void)
{
    uint32_t distance;                       // 存储超声波测距结果
    static uint32_t test_counter = 0;        // 测试周期计数器

    printf("=== 所有模块综合测试 - 第%lu轮 ===\n", ++test_counter);

    // 第一步：测试PIR人体感应传感器
    printf("1. PIR传感器测试: ");
    if (HAL_GPIO_ReadPin(PIR_SENSOR_GPIO_Port, PIR_SENSOR_Pin) == GPIO_PIN_SET)
    {
        printf("检测到人体运动\n");
    }
    else
    {
        printf("无人体运动\n");
    }

    // 第二步：测试超声波测距传感器
    distance = Ultrasonic_GetDistance();
    printf("2. 超声波传感器测试: ");
    if (distance > 0)
    {
        printf("距离 %lu cm\n", distance);
    }
    else
    {
        printf("测量错误\n");
    }

    // 第三步：测试舵机动作
    printf("3. 舵机测试: 转动到90度（开盖）\n");
    Servo_SetAngle(90);                      // 模拟开盖动作
    HAL_Delay(1000);                         // 保持1秒
    printf("   舵机测试: 转动到0度（关盖）\n");
    Servo_SetAngle(0);                       // 模拟关盖动作
    HAL_Delay(1000);                         // 保持1秒

    // 第四步：测试LED和蜂鸣器
    printf("4. LED和蜂鸣器测试: 开启\n");
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);     // 点亮LED
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_SET); // 开启蜂鸣器
    HAL_Delay(500);                          // 保持0.5秒

    printf("   LED和蜂鸣器测试: 关闭\n");
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);       // 熄灭LED
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_RESET); // 关闭蜂鸣器

    printf("=== 测试周期完成 ===\n\n");
    HAL_Delay(2000);                         // 等待2秒后进行下一轮测试
}

/**
  * @brief  Individual module test menu
  * @retval None
  * @note   Uncomment desired test in main() function
  */
void Run_Module_Tests(void)
{
    printf("Smart Trash Can Module Tests\n");
    printf("============================\n");
    
    // Uncomment ONE of the following tests:
    
    // Test_PIR_Sensor();        // Test PIR sensor only
    // Test_Servo_Motor();       // Test servo motor only
    // Test_Ultrasonic_Sensor(); // Test ultrasonic sensor only
    // Test_LED_Buzzer();        // Test LED and buzzer only
    Test_All_Modules();          // Test all modules in sequence
}

/**
  * @brief  Print system information
  * @retval None
  */
void Print_System_Info(void)
{
    printf("\n");
    printf("STM32F103C8T6 Smart Trash Can System\n");
    printf("====================================\n");
    printf("System Clock: %lu MHz\n", HAL_RCC_GetHCLKFreq() / 1000000);
    printf("APB1 Clock: %lu MHz\n", HAL_RCC_GetPCLK1Freq() / 1000000);
    printf("APB2 Clock: %lu MHz\n", HAL_RCC_GetPCLK2Freq() / 1000000);
    printf("\n");
    printf("Pin Configuration:\n");
    printf("- PIR Sensor: PA0\n");
    printf("- Buzzer: PA1\n");
    printf("- Servo PWM: PA8\n");
    printf("- Ultrasonic Echo: PA10\n");
    printf("- Ultrasonic Trig: PB0\n");
    printf("- LED: PC13\n");
    printf("\n");
    printf("Module Tests Available:\n");
    printf("- PIR Sensor Test\n");
    printf("- Servo Motor Test\n");
    printf("- Ultrasonic Sensor Test\n");
    printf("- LED and Buzzer Test\n");
    printf("- All Modules Test\n");
    printf("\n");
}

/* Note: To use these test functions, add the following to your main.c:

// In main() function, replace the main loop with:
int main(void)
{
    // ... (initialization code remains the same)
    
    // Print system information
    Print_System_Info();
    
    // Run module tests
    Run_Module_Tests();
    
    // Should never reach here
    while (1) {}
}

// Also add UART configuration for printf output:
// 1. Enable UART in CubeMX or add UART initialization
// 2. Add retarget printf to UART (implement _write function)
// 3. Connect UART TX to USB-Serial converter for monitoring

*/

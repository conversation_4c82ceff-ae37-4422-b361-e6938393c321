/**
  ******************************************************************************
  * @file    test_modules.c
  * @brief   Test functions for smart trash can modules
  * <AUTHOR> HAL Implementation
  * @date    2024
  ******************************************************************************
  * @attention
  * 
  * This file contains test functions for individual modules.
  * Uncomment the desired test in main() function to test specific modules.
  * 
  ******************************************************************************
  */

#include "main.h"

/* External variables */
extern TIM_HandleTypeDef htim1;

/* Test function prototypes */
void Test_PIR_Sensor(void);
void Test_Servo_Motor(void);
void Test_Ultrasonic_Sensor(void);
void Test_LED_Buzzer(void);
void Test_All_Modules(void);

/**
  * @brief  Test PIR sensor functionality
  * @retval None
  */
void Test_PIR_Sensor(void)
{
    printf("Testing PIR Sensor...\n");
    
    while(1)
    {
        if (HAL_GPIO_ReadPin(PIR_SENSOR_GPIO_Port, PIR_SENSOR_Pin) == GPIO_PIN_SET)
        {
            printf("Motion detected!\n");
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET); // LED on
            HAL_Delay(1000);
        }
        else
        {
            printf("No motion\n");
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET); // LED off
        }
        HAL_Delay(500);
    }
}

/**
  * @brief  Test servo motor functionality
  * @retval None
  */
void Test_Servo_Motor(void)
{
    printf("Testing Servo Motor...\n");
    
    while(1)
    {
        printf("Servo to 0 degrees\n");
        Servo_SetAngle(0);
        HAL_Delay(2000);
        
        printf("Servo to 45 degrees\n");
        Servo_SetAngle(45);
        HAL_Delay(2000);
        
        printf("Servo to 90 degrees\n");
        Servo_SetAngle(90);
        HAL_Delay(2000);
        
        printf("Servo to 135 degrees\n");
        Servo_SetAngle(135);
        HAL_Delay(2000);
        
        printf("Servo to 180 degrees\n");
        Servo_SetAngle(180);
        HAL_Delay(2000);
    }
}

/**
  * @brief  Test ultrasonic sensor functionality
  * @retval None
  */
void Test_Ultrasonic_Sensor(void)
{
    uint32_t distance;
    
    printf("Testing Ultrasonic Sensor...\n");
    
    while(1)
    {
        distance = Ultrasonic_GetDistance();
        
        if (distance > 0)
        {
            printf("Distance: %lu cm\n", distance);
            
            if (distance < 10)
            {
                printf("Object too close!\n");
                HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET); // LED on
            }
            else
            {
                HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET); // LED off
            }
        }
        else
        {
            printf("Measurement error or out of range\n");
        }
        
        HAL_Delay(1000);
    }
}

/**
  * @brief  Test LED and buzzer functionality
  * @retval None
  */
void Test_LED_Buzzer(void)
{
    printf("Testing LED and Buzzer...\n");
    
    while(1)
    {
        printf("LED and Buzzer ON\n");
        HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);     // LED on
        HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_SET); // Buzzer on
        HAL_Delay(1000);
        
        printf("LED and Buzzer OFF\n");
        HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);       // LED off
        HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_RESET); // Buzzer off
        HAL_Delay(1000);
    }
}

/**
  * @brief  Test all modules in sequence
  * @retval None
  */
void Test_All_Modules(void)
{
    uint32_t distance;
    static uint32_t test_counter = 0;
    
    printf("Testing All Modules - Cycle %lu\n", ++test_counter);
    
    // Test PIR sensor
    printf("1. PIR Sensor: ");
    if (HAL_GPIO_ReadPin(PIR_SENSOR_GPIO_Port, PIR_SENSOR_Pin) == GPIO_PIN_SET)
    {
        printf("Motion detected\n");
    }
    else
    {
        printf("No motion\n");
    }
    
    // Test ultrasonic sensor
    distance = Ultrasonic_GetDistance();
    printf("2. Ultrasonic: ");
    if (distance > 0)
    {
        printf("%lu cm\n", distance);
    }
    else
    {
        printf("Error\n");
    }
    
    // Test servo motor
    printf("3. Servo: Moving to 90 degrees\n");
    Servo_SetAngle(90);
    HAL_Delay(1000);
    printf("   Servo: Moving to 0 degrees\n");
    Servo_SetAngle(0);
    HAL_Delay(1000);
    
    // Test LED and buzzer
    printf("4. LED and Buzzer: ON\n");
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_SET);
    HAL_Delay(500);
    
    printf("   LED and Buzzer: OFF\n");
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(BUZZER_GPIO_Port, BUZZER_Pin, GPIO_PIN_RESET);
    
    printf("Test cycle complete\n\n");
    HAL_Delay(2000);
}

/**
  * @brief  Individual module test menu
  * @retval None
  * @note   Uncomment desired test in main() function
  */
void Run_Module_Tests(void)
{
    printf("Smart Trash Can Module Tests\n");
    printf("============================\n");
    
    // Uncomment ONE of the following tests:
    
    // Test_PIR_Sensor();        // Test PIR sensor only
    // Test_Servo_Motor();       // Test servo motor only
    // Test_Ultrasonic_Sensor(); // Test ultrasonic sensor only
    // Test_LED_Buzzer();        // Test LED and buzzer only
    Test_All_Modules();          // Test all modules in sequence
}

/**
  * @brief  Print system information
  * @retval None
  */
void Print_System_Info(void)
{
    printf("\n");
    printf("STM32F103C8T6 Smart Trash Can System\n");
    printf("====================================\n");
    printf("System Clock: %lu MHz\n", HAL_RCC_GetHCLKFreq() / 1000000);
    printf("APB1 Clock: %lu MHz\n", HAL_RCC_GetPCLK1Freq() / 1000000);
    printf("APB2 Clock: %lu MHz\n", HAL_RCC_GetPCLK2Freq() / 1000000);
    printf("\n");
    printf("Pin Configuration:\n");
    printf("- PIR Sensor: PA0\n");
    printf("- Buzzer: PA1\n");
    printf("- Servo PWM: PA8\n");
    printf("- Ultrasonic Echo: PA10\n");
    printf("- Ultrasonic Trig: PB0\n");
    printf("- LED: PC13\n");
    printf("\n");
    printf("Module Tests Available:\n");
    printf("- PIR Sensor Test\n");
    printf("- Servo Motor Test\n");
    printf("- Ultrasonic Sensor Test\n");
    printf("- LED and Buzzer Test\n");
    printf("- All Modules Test\n");
    printf("\n");
}

/* Note: To use these test functions, add the following to your main.c:

// In main() function, replace the main loop with:
int main(void)
{
    // ... (initialization code remains the same)
    
    // Print system information
    Print_System_Info();
    
    // Run module tests
    Run_Module_Tests();
    
    // Should never reach here
    while (1) {}
}

// Also add UART configuration for printf output:
// 1. Enable UART in CubeMX or add UART initialization
// 2. Add retarget printf to UART (implement _write function)
// 3. Connect UART TX to USB-Serial converter for monitoring

*/

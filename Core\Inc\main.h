/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : 智能垃圾桶主头文件
  * @description    : 包含智能垃圾桶系统的公共定义、引脚配置和函数声明
  *                   定义了各个传感器和执行器的GPIO引脚分配
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H                    // 防止头文件重复包含的宏定义
#define __MAIN_H

#ifdef __cplusplus                   // C++兼容性处理
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"          // 包含STM32F1xx系列HAL库头文件

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
// 用户自定义头文件包含区域（当前为空）
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
// 导出类型定义区域（当前为空）
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */
// 导出常量定义区域（当前为空）
/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */
// 导出宏定义区域（当前为空）
/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);           // 错误处理函数声明

/* USER CODE BEGIN EFP */
// 用户自定义函数声明区域（当前为空）
/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
// GPIO引脚定义 - 智能垃圾桶硬件接口配置

// PIR人体感应传感器 (HC-SR501)
#define PIR_SENSOR_Pin GPIO_PIN_0           // PIR传感器数据引脚：PA0
#define PIR_SENSOR_GPIO_Port GPIOA          // PIR传感器GPIO端口：GPIOA

// 蜂鸣器控制引脚
#define BUZZER_Pin GPIO_PIN_1               // 蜂鸣器控制引脚：PA1
#define BUZZER_GPIO_Port GPIOA              // 蜂鸣器GPIO端口：GPIOA

// 舵机PWM控制引脚
#define SERVO_Pin GPIO_PIN_8                // 舵机PWM信号引脚：PA8 (TIM1_CH1)
#define SERVO_GPIO_Port GPIOA               // 舵机GPIO端口：GPIOA

// 超声波传感器 (HC-SR04) - Echo回波接收引脚
#define ULTRASONIC_ECHO_Pin GPIO_PIN_10     // 超声波Echo引脚：PA10
#define ULTRASONIC_ECHO_GPIO_Port GPIOA     // 超声波Echo GPIO端口：GPIOA

// 超声波传感器 (HC-SR04) - Trig触发引脚
#define ULTRASONIC_TRIG_Pin GPIO_PIN_0      // 超声波Trig引脚：PB0
#define ULTRASONIC_TRIG_GPIO_Port GPIOB     // 超声波Trig GPIO端口：GPIOB

// LED指示灯引脚 (板载LED)
#define LED_Pin GPIO_PIN_13                 // LED控制引脚：PC13 (板载LED)
#define LED_GPIO_Port GPIOC                 // LED GPIO端口：GPIOC

/* USER CODE BEGIN Private defines */
// 用户自定义私有宏定义区域（当前为空）
/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
